<template>
  <div class="purchase-form">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      :disabled="mode === 'view'"
    >
      <el-form-item label="供应商" prop="supplierId">
        <el-select
          v-model="formData.supplierId"
          filterable
          placeholder="请选择供应商"
          style="width: 100%"
        >
          <el-option
            v-for="item in supplierOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="药品" prop="medicineId">
        <el-select
          v-model="formData.medicineId"
          filterable
          placeholder="请选择药品"
          style="width: 100%"
          @change="handleMedicineChange"
        >
          <el-option
            v-for="item in medicineOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="数量" prop="quantity">
        <el-input-number
          v-model="formData.quantity"
          :min="1"
          :precision="0"
          style="width: 100%"
          @change="calculateTotal"
        />
      </el-form-item>
      
      <el-form-item label="单价" prop="price">
        <el-input-number
          v-model="formData.price"
          :precision="2"
          :step="0.1"
          :min="0"
          style="width: 100%"
          @change="calculateTotal"
        />
      </el-form-item>
      
      <el-form-item label="总金额">
        <el-input
          :value="totalAmount.toFixed(2)"
          readonly
          style="width: 100%"
        >
          <template #prepend>¥</template>
        </el-input>
      </el-form-item>
      
      <el-form-item label="采购日期" prop="purchaseDate">
        <el-date-picker
          v-model="formData.purchaseDate"
          type="datetime"
          placeholder="请选择采购日期"
          style="width: 100%"
        />
      </el-form-item>
      
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio value="PENDING">待审批</el-radio>
          <el-radio value="APPROVED">已审批</el-radio>
          <el-radio value="COMPLETED">已完成</el-radio>
          <el-radio value="CANCELLED">已取消</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item>
        <el-button v-if="mode !== 'view'" type="primary" @click="handleSubmit">提交</el-button>
        <el-button @click="handleCancel">{{ mode === 'view' ? '返回' : '取消' }}</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';
import { getPurchaseDetail, addPurchase, updatePurchase } from '@/api/modules/purchase';
import { getAllSuppliers } from '@/api/modules/supplier';
import { getMedicineList } from '@/api/modules/medicine';
import type { Purchase, Supplier, Medicine } from '@/types';

// 定义组件的props
const props = defineProps<{
  id?: number;
  mode?: 'add' | 'edit' | 'view';
}>();

// 定义组件的事件
const emit = defineEmits<{
  (e: 'success'): void;
  (e: 'cancel'): void;
}>();

// 表单引用
const formRef = ref<FormInstance>();

// 表单数据
const formData = reactive<Purchase>({
  supplierId: undefined,
  medicineId: undefined,
  quantity: 1,
  price: 0,
  purchaseDate: new Date().toISOString(),
  status: 'PENDING'
});

// 表单验证规则
const rules = reactive<FormRules>({
  supplierId: [
    { required: true, message: '请选择供应商', trigger: 'change' }
  ],
  medicineId: [
    { required: true, message: '请选择药品', trigger: 'change' }
  ],
  quantity: [
    { required: true, message: '请输入数量', trigger: 'blur' }
  ],
  price: [
    { required: true, message: '请输入单价', trigger: 'blur' }
  ],
  purchaseDate: [
    { required: true, message: '请选择采购日期', trigger: 'change' }
  ]
});

// 选项数据
const supplierOptions = ref<{ label: string; value: number }[]>([]);
const medicineOptions = ref<{ label: string; value: number }[]>([]);
const medicineList = ref<Medicine[]>([]);

// 计算属性
const selectedMedicine = computed(() => {
  return medicineList.value.find(item => item.id === formData.medicineId);
});

const totalAmount = computed(() => {
  return (formData.quantity || 0) * (formData.price || 0);
});

// 获取供应商列表
const fetchSuppliers = async () => {
  try {
    const supplierList = await getAllSuppliers();
    supplierOptions.value = supplierList.map(item => ({
      label: item.name,
      value: item.id!
    }));
  } catch (error) {
    console.error('获取供应商列表失败:', error);
  }
};

// 获取药品列表
const fetchMedicines = async () => {
  try {
    const result = await getMedicineList({ page: 1, size: 1000 });
    medicineList.value = result.records;
    medicineOptions.value = result.records.map(item => ({
      label: `${item.name} (${item.spec})`,
      value: item.id!
    }));
  } catch (error) {
    console.error('获取药品列表失败:', error);
  }
};

// 获取采购详情
const fetchPurchaseDetail = async (id: number) => {
  try {
    const detail = await getPurchaseDetail(id);
    Object.keys(detail).forEach(key => {
      if (key in formData) {
        formData[key as keyof Purchase] = detail[key as keyof Purchase];
      }
    });
  } catch (error: any) {
    ElMessage.error(error.msg || '获取采购详情失败');
  }
};

// 处理药品选择变化
const handleMedicineChange = (medicineId: number) => {
  const medicine = medicineList.value.find(item => item.id === medicineId);
  if (medicine) {
    formData.price = medicine.price;
    calculateTotal();
  }
};

// 计算总金额
const calculateTotal = () => {
  // 总金额通过计算属性自动计算
};

// 处理提交
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const submitData = {
          ...formData,
          totalAmount: totalAmount.value
        };

        if (props.mode === 'edit' && props.id) {
          await updatePurchase(props.id, submitData);
          ElMessage.success('更新成功');
        } else {
          await addPurchase(submitData);
          ElMessage.success('添加成功');
        }
        emit('success');
      } catch (error: any) {
        ElMessage.error(error.msg || '操作失败');
      }
    }
  });
};

// 处理取消
const handleCancel = () => {
  emit('cancel');
};

// 页面加载时获取数据
onMounted(async () => {
  await fetchSuppliers();
  await fetchMedicines();
  
  if ((props.mode === 'edit' || props.mode === 'view') && props.id) {
    await fetchPurchaseDetail(props.id);
  }
});
</script>

<style scoped>
.purchase-form {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}
</style>